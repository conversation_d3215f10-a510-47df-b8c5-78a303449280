
export interface Subcategory {
  id: string;
  name: string;
  allocatedAmount: number;
  isComplete?: boolean; // New: for marking subcategory as complete
}

export interface Category {
  id: string;
  name: string;
  allocatedAmount: number;
  subcategories: Subcategory[];
  isAmountHidden?: boolean; // New: for hiding amounts for this category
}

export interface BudgetData {
  totalIncome: number;
  categories: Category[];
  selectedCurrency: string; // New: to store selected currency
  areGlobalAmountsHidden: boolean; // New: to store global visibility state
}

export type ModalType =
  | { type: 'addCategory' }
  | { type: 'editCategory'; category: Category }
  | { type: 'addSubcategory'; parentCategoryId: string }
  | { type: 'editSubcategory'; parentCategoryId: string; subcategory: Subcategory }
  | { type: 'deleteCategory'; category: Category }
  | { type: 'deleteSubcategory'; parentCategoryId: string; subcategory: Subcategory; parentCategoryName: string }
  | null;

// Props for forms might need to be extended for validation if not handled in App.tsx
export interface CategoryFormPropsBase {
  onSubmit: (name: string, allocatedAmount: number) => void;
  onClose: () => void;
  selectedCurrency: string; // For alert messages
}

export interface CategoryFormPropsNew extends CategoryFormPropsBase {
  existingCategory?: null;
  maxAllocatableAmount: number;
}
export interface CategoryFormPropsEdit extends CategoryFormPropsBase {
  existingCategory: Category;
  maxAllocatableAmount: number;
  minAllocatableAmountForEdit: number; // Min amount based on sum of subcategories
}

export type CategoryFormProps = CategoryFormPropsNew | CategoryFormPropsEdit;


export interface SubcategoryFormPropsBase {
  onSubmit: (name: string, allocatedAmount: number) => void;
  onClose: () => void;
  parentCategoryName?: string;
  selectedCurrency: string; // For alert messages
}
export interface SubcategoryFormPropsNew extends SubcategoryFormPropsBase {
  existingSubcategory?: null;
  maxAllocatableAmount: number;
}
export interface SubcategoryFormPropsEdit extends SubcategoryFormPropsBase {
  existingSubcategory: Subcategory;
  maxAllocatableAmount: number;
}

export type SubcategoryFormProps = SubcategoryFormPropsNew | SubcategoryFormPropsEdit;

// Toast Notification Types
export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastMessage {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
}